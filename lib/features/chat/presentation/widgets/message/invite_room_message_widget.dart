import 'package:flutter/material.dart';
import 'package:flutter_audio_room/core/extensions/context_ext.dart';
import 'package:flutter_audio_room/core/theme/text_theme.dart';
import 'package:flutter_audio_room/core/widgets/app_button.dart';
import 'package:flutter_audio_room/features/audio_room/presentation/mixins/join_room_mixin.dart';
import 'package:flutter_audio_room/features/chat/domain/extension/message_metadata_extension.dart';
import 'package:flutter_chat_types/flutter_chat_types.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class InviteRoomMessageWidget extends ConsumerStatefulWidget {
  final CustomMessage message;
  final String conversationId;

  const InviteRoomMessageWidget({
    super.key,
    required this.message,
    required this.conversationId,
  });

  @override
  ConsumerState<InviteRoomMessageWidget> createState() =>
      _InviteRoomMessageWidgetState();
}

class _InviteRoomMessageWidgetState
    extends ConsumerState<InviteRoomMessageWidget> with Join<PERSON>oomMixin {
  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surfaceContainerHighest,
        borderRadius: BorderRadius.circular(12.r),
      ),
      padding: EdgeInsets.all(12.w),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            widget.message.extend.roomInfo?.category?.name.toString() ?? '',
            style: context.textTheme.bodyMedium
                ?.copyWith(color: context.theme.colorScheme.primary),
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
          ),
          5.verticalSpace,
          Text(
            'I invite you to join the room',
            style: context.textTheme.bodyMediumSemiBold,
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
          ),
          5.verticalSpace,
          Text(
            widget.message.extend.roomInfo?.title ?? '',
            style: context.textTheme.bodySmall,
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
          ),
          12.verticalSpace,
          AppButton(
            width: 200.w,
            text: 'Join',
            onPressed: () {
              navigateToAudioRoom(widget.message.extend.roomInfo);
            },
          )
        ],
      ),
    );
  }
}
