import 'dart:io';

import 'package:flutter/material.dart';
import 'package:flutter_audio_room/core/widgets/video_player/video_player_controller.dart';
import 'package:flutter_audio_room/core/widgets/video_player/video_player_widget.dart';
import 'package:flutter_audio_room/features/chat/domain/extension/message_metadata_extension.dart';
import 'package:flutter_audio_room/features/chat/domain/models/message_extend_model.dart';
import 'package:flutter_audio_room/features/chat/presentation/providers/message_file_provider.dart';
import 'package:flutter_chat_types/flutter_chat_types.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

/// 视频消息组件
class VideoMessageWidget extends ConsumerWidget {
  final VideoMessage message;
  final String conversationId;

  const VideoMessageWidget({
    super.key,
    required this.message,
    required this.conversationId,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final fileMetadataState =
        ref.watch(messageFileProvider((conversationId, message)));

    // 计算视频尺寸，参考Telegram的设计
    final screenWidth = MediaQuery.of(context).size.width;
    final maxVideoWidth = screenWidth * 0.6; // 最大宽度为屏幕的60%
    final minVideoWidth = screenWidth * 0.3; // 最小宽度为屏幕的30%

    // 根据视频比例计算合适的尺寸
    double videoWidth = maxVideoWidth;
    double videoHeight = maxVideoWidth * 0.75; // 默认4:3比例

    // 如果有视频尺寸信息，使用实际比例
    if (message.width != null && message.height != null) {
      final aspectRatio = message.width! / message.height!;
      if (aspectRatio > 1) {
        // 横视频
        videoWidth = maxVideoWidth;
        videoHeight = videoWidth / aspectRatio;
        if (videoHeight < 120.h) videoHeight = 120.h;
      } else {
        // 竖视频或正方形
        videoHeight = maxVideoWidth;
        videoWidth = videoHeight * aspectRatio;
        if (videoWidth < minVideoWidth) {
          videoWidth = minVideoWidth;
          videoHeight = videoWidth / aspectRatio;
        }
      }
    }

    return SizedBox(
      width: videoWidth,
      height: videoHeight,
      child: Stack(
        children: [
            // 根据状态显示不同内容
            Positioned.fill(
              child: Builder(
                builder: (context) {
                  // 处理错误状态
                  if (fileMetadataState.hasError) {
                  return Container(
                    color: Theme.of(context).colorScheme.errorContainer,
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(
                          Icons.error_outline,
                          color: Theme.of(context).colorScheme.error,
                          size: 32.sp,
                        ),
                        SizedBox(height: 8.h),
                        Text(
                          'Failed to load',
                          style: TextStyle(
                            fontSize: 12.sp,
                            color: Theme.of(context).colorScheme.error,
                          ),
                        ),
                      ],
                    ),
                    );
                  }

                  // 处理加载状态
                  if (fileMetadataState.isLoading ||
                      !fileMetadataState.hasValue) {
                    return Container(
                    color: Colors.black.withValues(alpha: 0.8),
                    child: Center(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          const CircularProgressIndicator(
                            color: Colors.white,
                            strokeWidth: 2,
                          ),
                          SizedBox(height: 8.h),
                          Text(
                            'Loading...',
                            style: TextStyle(
                              color: Colors.white,
                              fontSize: 12.sp,
                            ),
                          ),
                        ],
                        ),
                      ),
                    );
                  }

                  // 处理成功状态
                  final metadata = fileMetadataState.value;
                  if (metadata == null) {
                    return Container(
                    color:
                        Theme.of(context).colorScheme.surfaceContainerHighest,
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(
                          Icons.video_library_outlined,
                          color: Theme.of(context)
                              .colorScheme
                              .onSurface
                              .withValues(alpha: 0.5),
                          size: 32.sp,
                        ),
                        SizedBox(height: 8.h),
                        Text(
                          'Video not found',
                          style: TextStyle(
                            fontSize: 12.sp,
                            color: Theme.of(context)
                                .colorScheme
                                .onSurface
                                .withValues(alpha: 0.5),
                          ),
                        ),
                      ],
                      ),
                    );
                  }

                  return GestureDetector(
                    onTap: () {
                      VideoPreviewModal.show(
                        context,
                        id: message.id,
                        videoPath: metadata.path,
                        thumbnailPath: metadata.thumbnailPath,
                        sourceType: VideoSourceType.file,
                        autoPlay: true,
                      );
                    },
                    child: Stack(
                      children: [
                      // 视频缩略图或占位符
                        Positioned.fill(
                          child: metadata.thumbnailPath != null
                              ? Image.file(
                                  File(metadata.thumbnailPath!),
                                  fit: BoxFit.cover,
                                )
                              : Container(
                                decoration: BoxDecoration(
                                  gradient: LinearGradient(
                                    begin: Alignment.topLeft,
                                    end: Alignment.bottomRight,
                                    colors: [
                                      Colors.grey.shade300,
                                      Colors.grey.shade400,
                                    ],
                                  ),
                                ),
                                  child: Center(
                                    child: Icon(
                                    Icons.video_file_outlined,
                                    size: 48.sp,
                                    color: Colors.white.withValues(alpha: 0.8),
                                    ),
                                  ),
                                ),
                        ),

                      // 播放按钮覆盖层
                      Positioned.fill(
                        child: Container(
                          decoration: BoxDecoration(
                            color: Colors.black.withValues(alpha: 0.3),
                          ),
                          child: Center(
                            child: Container(
                              width: 56.w,
                              height: 56.w,
                              decoration: BoxDecoration(
                                color: Colors.white.withValues(alpha: 0.9),
                                shape: BoxShape.circle,
                                boxShadow: [
                                  BoxShadow(
                                    color: Colors.black.withValues(alpha: 0.2),
                                    blurRadius: 8,
                                    offset: const Offset(0, 2),
                                  ),
                                ],
                              ),
                              child: Icon(
                                Icons.play_arrow_rounded,
                                color: Colors.black87,
                                size: 32.sp,
                              ),
                            ),
                          ),
                          ),
                        ),
                      ],
                    ),
                  );
                },
              ),
            ),

          // 时长指示器 - 放在左下角避免与加密指示器重叠
            if (message.duration != null)
              Positioned(
              left: 8.w,
                bottom: 8.h,
                child: Container(
                  padding: EdgeInsets.symmetric(
                  horizontal: 8.w,
                  vertical: 4.h,
                  ),
                  decoration: BoxDecoration(
                  color: Colors.black.withValues(alpha: 0.7),
                  borderRadius: BorderRadius.circular(12.r),
                  ),
                  child: Text(
                  _formatDuration(Duration(seconds: message.duration!)),
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: 12.sp,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
            ),

          // 显示文件下载状态覆盖层
          if (message.fileStatus != FileStatus.downloaded)
            Positioned.fill(
              child: Container(
                decoration: BoxDecoration(
                  color: Colors.black.withValues(alpha: 0.6),
                  borderRadius: BorderRadius.circular(12.r),
                ),
                child: Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      const CircularProgressIndicator(
                        strokeWidth: 3,
                        color: Colors.white,
                      ),
                      SizedBox(height: 8.h),
                      Text(
                        'Downloading...',
                        style: TextStyle(
                          color: Colors.white,
                          fontSize: 12.sp,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
        ],
      ),
    );
  }

  String _formatDuration(Duration duration) {
    final hours = duration.inHours;
    final minutes = duration.inMinutes.remainder(60);
    final seconds = duration.inSeconds.remainder(60);

    if (hours > 0) {
      return '$hours:${minutes.toString().padLeft(2, '0')}:${seconds.toString().padLeft(2, '0')}';
    } else {
      return '$minutes:${seconds.toString().padLeft(2, '0')}';
    }
  }
}
