import 'dart:io';

import 'package:flutter_audio_room/core/di/app_module.dart';
import 'package:flutter_audio_room/core/utils/loading_utils.dart';
import 'package:flutter_audio_room/core/utils/log_utils.dart';
import 'package:flutter_audio_room/features/authentication/presentation/providers/auth_providers.dart';
import 'package:flutter_audio_room/features/chat/domain/extension/message_metadata_extension.dart';
import 'package:flutter_audio_room/features/chat/domain/models/message_config.dart';
import 'package:flutter_audio_room/features/chat/domain/models/message_extend_model.dart';
import 'package:flutter_audio_room/features/chat/domain/models/message_subtype.dart';
import 'package:flutter_audio_room/features/chat/domain/models/remote_message_model.dart';
import 'package:flutter_audio_room/features/chat/presentation/controllers/chat_socket_provider.dart';
import 'package:flutter_audio_room/features/chat/presentation/providers/base/chat_provider_base.dart';
import 'package:flutter_audio_room/features/chat/presentation/providers/conversation_provider.dart';
import 'package:flutter_audio_room/services/file_service/i_file_service.dart';
import 'package:flutter_audio_room/services/file_upload_service/file_upload_service.dart';
import 'package:flutter_audio_room/services/file_upload_service/i_file_upload_service.dart';
import 'package:flutter_audio_room/services/signal_protocol_service/repository/signal_repository_provider.dart';
import 'package:flutter_audio_room/services/signal_protocol_service/signal_message_provider.dart';
import 'package:flutter_audio_room/shared/domain/models/either.dart';
import 'package:flutter_audio_room/shared/domain/types/common_types.dart';
import 'package:flutter_audio_room/shared/exceptions/app_exception.dart';
import 'package:flutter_chat_types/flutter_chat_types.dart' as types;
import 'package:flutter_chat_types/flutter_chat_types.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:path/path.dart';

mixin SendMessageMixin on ChatProviderBase {

  /// 统一消息发送入口
  /// [config] 消息配置
  /// [type] 消息类型
  Future<VoidResult> sendMessage(MessageConfig config) async {
    final conversationNotifier = ref.read(conversationProvider.notifier);
    final conversationResult =
        await conversationNotifier.getOrCreateConversation(conversationId);
    if (conversationResult.isLeft()) {
      LoadingUtils.showToast('Send message failed, please try again later');
      return const Left(AppException(
        message: 'Send message failed, please try again later',
        statusCode: 500,
        identifier: 'send_message_failed',
      ));
    }

    final conversation = conversationResult.getRight()!;
    final dIdChanged = conversation.extend.dIdChanged;

    LogUtils.d('peer device changed: $dIdChanged', tag: 'SendMessageMixin');

    if (dIdChanged) {
      await conversationNotifier.clearNewDeviceTag(conversationId);
    }

    // 检查session有效性
    final hasSession =
        await ref.read(signalMessageProvider.notifier).hasValidSession(
              ref.read(accountProvider).userInfo?.profile?.id ?? '',
              conversationId,
            );

    if (!hasSession || dIdChanged) {
      final res = await initializeSignalSession(conversationId);
      if (res.isLeft()) {
        return Left(res.getLeft()!);
      }
    }

    switch (config) {
      case TextMessageConfig():
        return _sendTextMessage(config);
      case ImageMessageConfig():
        return _sendImageMessage(config);
      case AudioMessageConfig():
        return _sendAudioMessage(config);
      case VideoMessageConfig():
        return _sendVideoMessage(config);
      case VoiceCallMessageConfig():
      case InviteRoomMessageConfig():
      case VoiceCallEndMessageConfig():
        return _sendCustomMessage(config);
    }
  }

  /// Initialize signal protocol session for a user
  ///
  /// Returns a [VoidResult] indicating success or failure
  Future<VoidResult> initializeSignalSession(String remoteUserId) async {
    try {
      final userId = ref.read(accountProvider).userInfo?.profile?.id ?? '';
      final signalMessage = ref.read(signalMessageProvider.notifier);

      // 获取对方的用户信息以获取他们的 Bundle
      final userBundle = await ref
          .read(signalRepositoryProvider.notifier)
          .getKeyBundle(userId: remoteUserId);

      if (userBundle.isLeft()) {
        return Left(userBundle.getLeft()!);
      }

      final keyBundle = userBundle.getRight()!;

      LogUtils.d('get peer user keyBundle: $keyBundle',
          tag: 'sendMessageMixin');

      // 使用对方的 Bundle 建立会话
      await signalMessage.recreateSession(
        userId,
        remoteUserId,
        keyBundle,
      );
      return const Right(null);
    } catch (e) {
      LogUtils.e('Failed to initialize signal session: ${e.toString()}',
          tag: 'sendMessageMixin');
      return Left(SignalEncryptionFailure(
          'Failed to initialize signal session: ${e.toString()}'));
    }
  }
  
  /// 创建消息基础对象
  Future<types.Message> _createMessageBase({
    required String userId,
    required types.MessageType type,
    String? content,
    String? filePath,
    int? size,
    int? duration,
    String? thumbnailPath,
    bool isEphemeral = true, // 默认启用阅后即焚
    String? fileId, // 添加fileId参数
  }) async {
    final now = DateTime.now().millisecondsSinceEpoch;
    final id = now.toString();
    final createdAt = now;
    final author = types.User(id: userId);

    final ephemeralTimeout = await ref
        .read(conversationProvider.notifier)
        .getEphemeralTimeout(conversationId);

    // 构建基础消息元数据，包括阅后即焚属性和fileId
    final metadata = MessageExtendModel(
      ephemeral: isEphemeral,
      msgCTtlS: ephemeralTimeout,
      thumbnailUrl: thumbnailPath,
      duration: duration,
      fileSize: size,
      fileId: fileId,
      fileStatus: FileStatus.pending,
    );

    // 为不同类型的消息添加阅后即焚属性
    types.Message message;

    switch (type) {
      case types.MessageType.text:
        message = types.TextMessage(
          author: author,
          id: id,
          text: content ?? '',
          status: types.Status.sending,
          createdAt: createdAt,
          metadata: metadata.toJson(),
        );
        break;
      case types.MessageType.image:
        message = ImageMessage(
          author: author,
          id: id,
          uri: filePath!,
          status: types.Status.sending,
          size: size!,
          name: basename(filePath),
          createdAt: createdAt,
          metadata: metadata.toJson(),
        );
        break;
      case types.MessageType.audio:
        message = AudioMessage(
          author: author,
          id: id,
          uri: filePath!,
          status: types.Status.sending,
          duration: Duration(seconds: duration ?? 0),
          name: basename(filePath),
          size: size!,
          createdAt: createdAt,
          metadata: metadata.toJson(),
        );
        break;
      case types.MessageType.video:
        message = VideoMessage(
          author: author,
          id: id,
          uri: filePath!,
          status: types.Status.sending,
          name: basename(filePath),
          size: size!,
          metadata: metadata.toJson(),
          createdAt: createdAt,
        );
        break;
      default:
        throw Exception('Unsupported message type');
    }

    ref
        .read(conversationProvider.notifier)
        .updateLastMessage(conversationId, message);

    return message;
  }

  Future<types.Message> _createCustomMessage({
    required MessageConfig config,
    bool isEphemeral = true,
  }) async {
    final now = DateTime.now().millisecondsSinceEpoch;
    final id = now.toString();
    final createdAt = now;
    final author = types.User(id: config.userId);

    final ephemeralTimeout = await ref
        .read(conversationProvider.notifier)
        .getEphemeralTimeout(conversationId);

    var metadata = MessageExtendModel(
      ephemeral: isEphemeral,
      msgCTtlS: ephemeralTimeout,
    );

    switch (config) {
      case InviteRoomMessageConfig():
        metadata = metadata.copyWith(
          roomInfo: config.roomInfo,
          subtype: MessageSubtype.INVITE_ROOM,
        );
        break;
      case VoiceCallEndMessageConfig():
        metadata = metadata.copyWith(
          callId: config.callId,
          callerId: config.callerId,
          calleeId: config.calleeId,
          callDuration: config.duration,
          callEndType: config.callEndType,
          subtype: MessageSubtype.VOICE_CALL,
        );
        break;
      default:
        throw Exception('Unsupported message type');
    }

    final message = types.CustomMessage(
      author: author,
      id: id,
      status: types.Status.sending,
      createdAt: createdAt,
      metadata: metadata.toJson(),
    );

    ref
        .read(conversationProvider.notifier)
        .updateLastMessage(conversationId, message);

    return message;
  }

  /// 处理文件类型消息发送
  Future<VoidResult> _handleFileMessage({
    required MessageConfig config,
    required types.MessageType messageType,
    required String filePath,
    int? duration,
    String? thumbnailPath,
    int? size,
  }) async {
    final messageList = await future;

    final fileId = switch (config) {
      TextMessageConfig() => null,
      ImageMessageConfig() => config.fileId,
      AudioMessageConfig() => config.fileId,
      VideoMessageConfig() => config.fileId,
      InviteRoomMessageConfig() => null,
      VoiceCallMessageConfig() => null,
      VoiceCallEndMessageConfig() => null,
    };

    // 1. 先创建本地消息并存入state
    final message = await _createMessageBase(
      userId: config.userId,
      type: messageType,
      filePath: filePath,
      duration: duration,
      thumbnailPath: thumbnailPath,
      size: size,
      fileId: fileId,
    );

    // 先将消息添加到state中
    state = AsyncData([message, ...messageList]);

    // 先存储到本地
    await messageLocalRepository.createMessage(message);

    // 同步更新最后一条消息
    await ref
        .read(conversationProvider.notifier)
        .updateLastMessage(conversationId, message);

    return _handleMessageResult(message, () async {
      try {
        final signalMessage = ref.read(signalMessageProvider.notifier);
        final fileBytes = await File(filePath).readAsBytes();

        // 2. 加密文件 - 使用接收方的用户ID而不是conversationId
        final receiverId = conversationId; // conversationId就是接收方的用户ID
        final (type, encryptedBytes) = await signalMessage.encryptFile(
          config.userId,
          receiverId,
          fileBytes,
        );



        // 创建加密文件
        final encryptedFilePath =
            '${File(filePath).parent.path}/encrypted_${basename(filePath)}';
        final encryptedFile = File(encryptedFilePath);
        await encryptedFile.writeAsBytes(encryptedBytes);

        // 3. 如果有缩略图，也需要加密
        String? encryptedThumbnailPath;
        if (thumbnailPath != null) {
          final thumbnailBytes = await File(thumbnailPath).readAsBytes();
          final (type, encryptedThumbnailBytes) =
              await signalMessage.encryptFile(
            config.userId,
            receiverId,
            thumbnailBytes,
          );

          encryptedThumbnailPath =
              '${File(thumbnailPath).parent.path}/encrypted_thumb_${basename(thumbnailPath)}';
          final encryptedThumbnailFile = File(encryptedThumbnailPath);
          await encryptedThumbnailFile.writeAsBytes(encryptedThumbnailBytes);
        }

        try {
          // 4. 上传加密后的文件
          final fileType = _getFileTypeFromMessageType(messageType);
          final fileUploadService = getIt<IFileUploadService>();

          final uploadResult = await fileUploadService.uploadFile(
            encryptedFile,
            fileType: fileType,
            fileScene: FileScene.CHAT,
            onProgress: (progress) {},
          );

          if (uploadResult.isLeft()) {
            await _updateMessageAfterUpload(
              message,
              types.Status.error,
              FileStatus.failed,
            );
            return Left(uploadResult.getLeft()!);
          }

          final fileUrl = uploadResult.getRight()!;

          // 5. 如果有缩略图，也上传
          String? thumbnailUrl;
          if (encryptedThumbnailPath != null) {
            final thumbnailUploadResult = await fileUploadService.uploadFile(
              File(encryptedThumbnailPath),
              fileType: FileType.image,
              fileScene: FileScene.CHAT,
            );

            if (thumbnailUploadResult.isRight()) {
              thumbnailUrl = thumbnailUploadResult.getRight()!;
            }
          }

          // 6. 创建带有文件URL的extend信息
          final messageExtend = message.extend.copyWith(
            fileUrl: fileUrl,
            thumbnailUrl: thumbnailUrl,
            duration: duration,
            fileName: basename(filePath),
            fileSize: size,
            fileId: '',
          );

          // 7. 复制消息并添加扩展信息
          final updatedMessage = message.copyWith(
            metadata: messageExtend.toJson(),
          );

          // 8. 更新本地消息
          final localMessage = await _updateMessageAfterUpload(
            message,
            types.Status.sending,
            FileStatus.downloaded,
          );

          // 9. 发送消息到远程
          return _sendToRemote(localMessage, updatedMessage);
        } finally {
          // 清理加密的临时文件
          await encryptedFile.delete();
          if (encryptedThumbnailPath != null) {
            await File(encryptedThumbnailPath).delete();
          }
        }
      } catch (e) {
        LogUtils.e('Error processing file message: $e',
            tag: 'SendMessageMixin');
        _updateMessageAfterUpload(
          message,
          types.Status.error,
          FileStatus.failed,
        );
        return Left(AppException(
          message: '文件处理失败: ${e.toString()}',
          statusCode: 500,
          identifier: 'file_processing_error',
        ));
      }
    });
  }

  // 根据消息类型获取文件类型
  FileType _getFileTypeFromMessageType(types.MessageType type) {
    switch (type) {
      case types.MessageType.image:
        return FileType.image;
      case types.MessageType.video:
        return FileType.video;
      case types.MessageType.audio:
        return FileType.audio;
      default:
        return FileType.file;
    }
  }

  // 上传后更新消息状态
  Future<Message> _updateMessageAfterUpload(
    Message updatedMessage,
    types.Status status,
    FileStatus fileStatus,
  ) async {
    final messageList = await future;

    final newMetadata = updatedMessage.extend.copyWith(fileStatus: fileStatus);
    final newMessage =
        updatedMessage.copyWith(status: status, metadata: newMetadata.toJson());

    final updatedMessages = messageList.map((msg) {
      if (msg.id == newMessage.id) {
        return newMessage;
      }
      return msg;
    }).toList();

    state = AsyncData(updatedMessages);
    await messageLocalRepository.updateMessage(newMessage);

    return newMessage;
  }

  Future<VoidResult> _sendToRemote(
    Message createdMessage,
    Message encryptedMessage,
  ) async {
    final messageToSend = RemoteMessageModel.fromLocalMessage(
      encryptedMessage,
      conversationId,
    ).toJson();
    LogUtils.d('message to send: ${messageToSend.toString()}',
        tag: 'SendMessageMixin');
    await ref.read(chatSocketProvider.notifier).emitWithAck(
        ChatSocketEvents.messageSend,
        messageToSend, (data) {
      final messageId = data?['data']?['id'] ?? '';
      if (data?['success'] == true) {
        _updateSendingMessageStatus(
          messageId,
          localMessage: createdMessage,
          status: types.Status.sent,
        );
      } else {
        _updateSendingMessageStatus(
          messageId,
          localMessage: createdMessage,
          status: types.Status.error,
        );
      }
    });

    return const Right(null);
  }

  Future<void> _updateSendingMessageStatus(
    String? id, {
    required Message localMessage,
    required types.Status status,
  }) async {
    final messageList = await future;
    final newMessage = localMessage.copyWith(status: status, remoteId: id);
    final updatedMessages = messageList.map((msg) {
      if (msg.id == localMessage.id) {
        return newMessage;
      }
      return msg;
    }).toList();

    state = AsyncData(updatedMessages);
    await messageLocalRepository.updateMessage(
      newMessage,
    );
  }

  /// 处理消息发送结果
  Future<VoidResult> _handleMessageResult(
    Message message,
    Future<VoidResult> Function() sendFn,
  ) async {
    try {
      final result = await sendFn();
      return result.fold(
        (failure) {
          return Left(failure);
        },
        (_) {
          return const Right(null);
        },
      );
    } catch (e) {
      LogUtils.e('Error sending message: $e', tag: 'ChatProvider');
      return Left(AppException(
        message: e.toString(),
        statusCode: 500,
        identifier: 'send_message_error',
      ));
    }
  }

  Future<VoidResult> _sendTextMessage(TextMessageConfig config) async {
    final messageList = await future;
    
    // 确保content不为空
    if (config.content.isEmpty) {
      return const Left(AppException(
        message: '消息内容不能为空',
        statusCode: 400,
        identifier: 'empty_message_content',
      ));
    }
    
    final message = await _createMessageBase(
      userId: config.userId,
      type: config.type,
      content: config.content,
    ) as types.TextMessage;

    state = AsyncData([message, ...messageList]);

    // 先存储到本地
    await messageLocalRepository.createMessage(message);

    // 同步更新最后一条消息
    await ref
        .read(conversationProvider.notifier)
        .updateLastMessage(conversationId, message);

    return _handleMessageResult(message, () async {
      final signalMessage = ref.read(signalMessageProvider.notifier);
      // 由于上面已经检查过content不为空，这里可以安全使用!
      final (type, content) = await signalMessage.encryptMessage(
        config.userId,
        conversationId,
        config.content,
      );

      final encryptedMessage = message.copyWith(
        text: content,
        metadata: message.extend
            .copyWith(
              isPreKeyMessage: type == 3,
            )
            .toJson(),
      );
      return _sendToRemote(message, encryptedMessage);
    });
  }

  Future<VoidResult> _sendImageMessage(ImageMessageConfig config) async {
    final fileMetadata = await fileService.getFileMetadata(config.fileId);
    if (fileMetadata.isLeft()) {
      return Left(fileMetadata.getLeft()!);
    }

    return _handleFileMessage(
      config: config,
      messageType: config.type,
      filePath: fileMetadata.getRight()!.path,
      size: fileMetadata.getRight()!.size,
    );
  }

  Future<VoidResult> _sendAudioMessage(AudioMessageConfig config) async {
    final fileMetadata = await fileService.getFileMetadata(config.fileId);
    if (fileMetadata.isLeft()) {
      return Left(fileMetadata.getLeft()!);
    }

    return _handleFileMessage(
      config: config,
      messageType: config.type,
      filePath: fileMetadata.getRight()!.path,
      size: fileMetadata.getRight()!.size,
      duration: config.duration,
    );
  }

  Future<VoidResult> _sendVideoMessage(VideoMessageConfig config) async {
    final fileMetadata = await fileService.getFileMetadata(config.fileId);
    if (fileMetadata.isLeft()) {
      return Left(fileMetadata.getLeft()!);
    }

    return _handleFileMessage(
      config: config,
      messageType: config.type,
      filePath: fileMetadata.getRight()!.path,
      size: fileMetadata.getRight()!.size,
      duration: config.duration,
      thumbnailPath: fileMetadata.getRight()!.thumbnailPath,
    );
  }

  Future<VoidResult> _sendCustomMessage(MessageConfig config) async {
    final messageList = await future;
    final message = await _createCustomMessage(
      config: config,
      isEphemeral: true,
    );

    state = AsyncData([message, ...messageList]);

    await messageLocalRepository.createMessage(message);

    await ref
        .read(conversationProvider.notifier)
        .updateLastMessage(conversationId, message);

    return _handleMessageResult(message, () async {
      return _sendToRemote(message, message);
    });
  }

  Future<VoidResult> retryMessage(Message message) async {
    LogUtils.d('重试发送消息: ${message.id}', tag: 'SendMessageMixin');

    try {
      // 检查session有效性
      final result = await _ensureValidSession();
      if (result.isLeft()) {
        return result;
      }

      // 先将消息状态更新为sending
      final updatedMessage = await _updateMessageStatus(
        message,
        types.Status.sending,
        message.fileStatus ?? FileStatus.pending,
      );

      // 根据消息类型处理重试
      switch (message.type) {
        case types.MessageType.text:
          return _retryTextMessage(updatedMessage as types.TextMessage);
        case types.MessageType.image:
        case types.MessageType.video:
        case types.MessageType.audio:
          return _retryFileMessage(updatedMessage);
        default:
          return const Left(AppException(
            message: '不支持的消息类型重试',
            statusCode: 400,
            identifier: 'unsupported_retry_message_type',
          ));
      }
    } catch (e) {
      LogUtils.e('重试消息失败: $e', tag: 'SendMessageMixin');
      return Left(AppException(
        message: '重试消息失败: ${e.toString()}',
        statusCode: 500,
        identifier: 'retry_message_error',
      ));
    }
  }

  /// 确保Signal会话有效
  Future<VoidResult> _ensureValidSession() async {
    final userId =
        ref.read(accountProvider).userInfo?.profile?.id ?? '';
    final hasSession =
        await ref.read(signalMessageProvider.notifier).hasValidSession(
              userId,
              conversationId,
            );

    if (!hasSession) {
      return initializeSignalSession(conversationId);
    }

    return const Right(null);
  }

  /// 更新消息状态的封装方法
  Future<Message> _updateMessageStatus(
    Message message,
    types.Status status,
    FileStatus fileStatus,
  ) async {
    return _updateMessageAfterUpload(message, status, fileStatus);
  }

  /// 重试发送文本消息
  Future<VoidResult> _retryTextMessage(types.TextMessage message) async {
    return _handleMessageResult(message, () async {
      final signalMessage = ref.read(signalMessageProvider.notifier);
      final userId =
          ref.read(accountProvider).userInfo?.profile?.id ?? '';

      // 加密消息内容
      final (type, content) = await signalMessage.encryptMessage(
        userId,
        conversationId,
        message.text,
      );

      // 创建加密后的消息对象
      final encryptedMessage = message.copyWith(
        text: content,
        metadata: message.extend
            .copyWith(
              isPreKeyMessage: type == 3,
            )
            .toJson(),
      );

      // 发送到远程
      return _sendToRemote(message, encryptedMessage);
    });
  }

  /// 重试发送文件消息
  Future<VoidResult> _retryFileMessage(Message message) async {
    // 验证文件ID
    final fileId = message.fileId;
    if (fileId == null || fileId.isEmpty) {
      return const Left(AppException(
        message: '文件ID不存在，无法重试发送',
        statusCode: 400,
        identifier: 'file_id_missing',
      ));
    }

    // 根据文件状态决定重试策略
    return message.fileStatus == FileStatus.failed
        ? _retryFailedFileUpload(message, fileId)
        : _retryFileMessageWithURL(message);
  }

  /// 重试上传失败的文件
  Future<VoidResult> _retryFailedFileUpload(
      Message message, String fileId) async {
    try {
      // 获取文件信息
      final fileMetadataResult = await fileService.getFileMetadata(fileId);
      if (fileMetadataResult.isLeft()) {
        return Left(fileMetadataResult.getLeft()!);
      }

      final metadata = fileMetadataResult.getRight()!;

      // 加密并上传文件
      final uploadResult = await _encryptAndUploadFile(
        message,
        metadata.path,
        metadata.thumbnailPath,
        metadata.size,
      );

      if (uploadResult.isLeft()) {
        return uploadResult;
      }

      final uploadData = uploadResult.getRight()!;

      // 创建带有文件URL的extend信息
      final messageExtend = message.extend.copyWith(
        fileUrl: uploadData.fileUrl,
        thumbnailUrl: uploadData.thumbnailUrl,
        duration: message.duration,
        fileName: basename(metadata.path),
        fileSize: metadata.size,
        fileId: '',
      );

      // 创建带有加密URL的消息
      final updatedMessage = message.copyWith(
        metadata: messageExtend.toJson(),
        status: types.Status.sending,
      );

      // 更新本地消息
      await messageLocalRepository.updateMessage(updatedMessage);

      // 发送消息到远程
      return _sendToRemote(message, updatedMessage);
    } catch (e) {
      LogUtils.e('重试上传文件失败: $e', tag: 'SendMessageMixin');
      await _updateMessageStatus(
        message,
        types.Status.error,
        FileStatus.failed,
      );
      return Left(AppException(
        message: '重试上传文件失败: ${e.toString()}',
        statusCode: 500,
        identifier: 'retry_file_upload_error',
      ));
    }
  }

  /// 加密并上传文件
  Future<ResultWithData<_UploadResult>> _encryptAndUploadFile(
    Message message,
    String filePath,
    String? thumbnailPath,
    int fileSize,
  ) async {
    File? encryptedFile;
    File? encryptedThumbnailFile;

    try {
      final userId =
          ref.read(accountProvider).userInfo?.profile?.id ?? '';
      final signalMessage = ref.read(signalMessageProvider.notifier);

      // 读取并加密主文件
      final fileBytes = await File(filePath).readAsBytes();
      final receiverId = conversationId; // conversationId就是接收方的用户ID
      final (type, encryptedBytes) = await signalMessage.encryptFile(
        userId,
        receiverId,
        fileBytes,
      );



      final encryptedFilePath =
          '${File(filePath).parent.path}/encrypted_${basename(filePath)}';
      encryptedFile = File(encryptedFilePath);
      await encryptedFile.writeAsBytes(encryptedBytes);

      // 处理缩略图（如果有）
      String? encryptedThumbnailPath;
      if (thumbnailPath != null) {
        final thumbnailBytes = await File(thumbnailPath).readAsBytes();
        final (type, encryptedThumbnailBytes) = await signalMessage.encryptFile(
          userId,
          receiverId,
          thumbnailBytes,
        );
        
        encryptedThumbnailPath =
            '${File(thumbnailPath).parent.path}/encrypted_thumb_${basename(thumbnailPath)}';
        encryptedThumbnailFile = File(encryptedThumbnailPath);
        await encryptedThumbnailFile.writeAsBytes(encryptedThumbnailBytes);
      }

      // 上传加密后的文件
      final fileType = _getFileTypeFromMessageType(message.type);
      final fileUploadService = getIt<IFileUploadService>();
      
      final uploadResult = await fileUploadService.uploadFile(
        encryptedFile,
        fileType: fileType,
        fileScene: FileScene.CHAT,
        onProgress: (progress) {},
      );
      
      if (uploadResult.isLeft()) {
        await _updateMessageStatus(
          message,
          types.Status.error,
          FileStatus.failed,
        );
        return Left(uploadResult.getLeft()!);
      }

      final fileUrl = uploadResult.getRight()!;
      
      // 如果有缩略图，也上传
      String? thumbnailUrl;
      if (encryptedThumbnailFile != null) {
        final thumbnailUploadResult = await fileUploadService.uploadFile(
          encryptedThumbnailFile,
          fileType: FileType.image,
          fileScene: FileScene.CHAT,
        );
        
        if (thumbnailUploadResult.isRight()) {
          thumbnailUrl = thumbnailUploadResult.getRight()!;
        }
      }

      return Right(_UploadResult(
        fileUrl: fileUrl,
        thumbnailUrl: thumbnailUrl,
      ));
    } finally {
      // 清理加密的临时文件
      await encryptedFile?.delete();
      await encryptedThumbnailFile?.delete();
    }
  }

  /// 使用现有URL重试发送文件消息
  Future<VoidResult> _retryFileMessageWithURL(Message message) async {
    try {
      final fileUrl = message.fileUrl;
      final thumbnailUrl = message.extend.thumbnailUrl;
      
      // 验证文件URL是否存在
      if (fileUrl == null || fileUrl.isEmpty) {
        return const Left(AppException(
          message: '文件URL不存在，无法重试发送',
          statusCode: 400,
          identifier: 'file_url_missing',
        ));
      }

      // 验证文件是否存在
      final file = File(fileUrl);
      if (!await file.exists()) {
        return const Left(AppException(
          message: '本地文件不存在，无法重试发送',
          statusCode: 404,
          identifier: 'local_file_not_found',
        ));
      }
      
      final filePath = fileUrl;
      LogUtils.d('使用本地文件: $filePath', tag: 'SendMessageMixin');

      // 验证缩略图是否存在（如果有）
      String? thumbnailPath;
      if (thumbnailUrl != null && thumbnailUrl.isNotEmpty) {
        final thumbnailFile = File(thumbnailUrl);
        if (await thumbnailFile.exists()) {
          thumbnailPath = thumbnailUrl;
          LogUtils.d('使用本地缩略图: $thumbnailPath', tag: 'SendMessageMixin');
        } else {
          LogUtils.w('本地缩略图不存在: $thumbnailUrl', tag: 'SendMessageMixin');
        }
      }
      
      // 加密并上传文件
      final uploadResult = await _encryptAndUploadFile(
        message,
        filePath,
        thumbnailPath,
        message.fileSize ?? 0,
      );

      if (uploadResult.isLeft()) {
        return uploadResult;
      }

      final uploadData = uploadResult.getRight()!;
      
      // 创建带有文件URL的extend信息
      final messageExtend = message.extend.copyWith(
        fileUrl: uploadData.fileUrl,
        thumbnailUrl: uploadData.thumbnailUrl,
        duration: message.duration,
        fileName: message.extend.fileName ?? basename(filePath),
        fileSize: message.fileSize,
        fileId: '',
      );
      
      // 创建带有加密URL的消息
      final updatedMessage = message.copyWith(
        metadata: messageExtend.toJson(),
        status: types.Status.sending,
      );
      
      // 更新本地消息
      await messageLocalRepository.updateMessage(updatedMessage);
      
      // 发送消息到远程
      return _sendToRemote(message, updatedMessage);
    } catch (e) {
      LogUtils.e('重试发送文件消息失败: $e', tag: 'SendMessageMixin');
      await _updateMessageStatus(
        message,
        types.Status.error,
        FileStatus.failed,
      );
      return Left(AppException(
        message: '重试发送文件消息失败: ${e.toString()}',
        statusCode: 500,
        identifier: 'retry_file_message_error',
      ));
    }
  }
}

/// 文件上传结果
class _UploadResult {
  final String fileUrl;
  final String? thumbnailUrl;

  _UploadResult({
    required this.fileUrl,
    this.thumbnailUrl,
  });
}
