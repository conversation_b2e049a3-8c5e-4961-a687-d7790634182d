import 'package:flutter_audio_room/core/di/app_module.dart';
import 'package:flutter_audio_room/core/utils/log_utils.dart';
import 'package:flutter_audio_room/features/authentication/presentation/providers/auth_providers.dart';
import 'package:flutter_audio_room/features/chat/domain/extension/message_metadata_extension.dart';
import 'package:flutter_audio_room/features/chat/domain/models/message_extend_model.dart';
import 'package:flutter_audio_room/features/chat/domain/models/message_subtype.dart';
import 'package:flutter_audio_room/features/chat/domain/models/remote_message_model.dart';
import 'package:flutter_audio_room/features/chat/presentation/providers/base/chat_provider_base.dart';
import 'package:flutter_audio_room/services/file_service/i_file_service.dart';
import 'package:flutter_audio_room/services/signal_protocol_service/signal_message_provider.dart';
import 'package:flutter_audio_room/shared/domain/models/either.dart';
import 'package:flutter_audio_room/shared/domain/types/common_types.dart';
import 'package:flutter_audio_room/shared/exceptions/app_exception.dart';
import 'package:flutter_chat_types/flutter_chat_types.dart';

mixin ReceiveMessageMixin on ChatProviderBase {
  Future<ResultWithData<Message>> onReceiveMessage(
      RemoteMessageModel message) async {
    final result = await _decryptMessage(message.toMessage());
    return await result.fold(
      (error) {
        LogUtils.e('Error decrypting message: $error', tag: 'receiveMessage');
        return Left(error);
      },
      (decryptedMessage) async {
        LogUtils.d('decryptedMessage: ${decryptedMessage.toJson().toString()}',
            tag: 'ReceiveMessageMixin');
        if (decryptedMessage.type == MessageType.custom) {
          if (decryptedMessage.extend.subtype == MessageSubtype.DEVICE_CHANGE) {
            return Right(decryptedMessage);
          }
        }

        return addMessage(
          decryptedMessage,
        );
      },
    );
  }

  Future<ResultWithData<Message>> _decryptMessage(Message message) async {
    switch (message.type) {
      case MessageType.text:
        return _decryptTextMessage(message);
      case MessageType.image:
        return _processMediaMessage(
            message as ImageMessage, 'decryptImageMessage');
      case MessageType.audio:
        return _processMediaMessage(
            message as AudioMessage, 'decryptAudioMessage');
      case MessageType.video:
        return _processMediaMessage(
            message as VideoMessage, 'decryptVideoMessage');
      case MessageType.system:
        return _onReceiveSystemMessage(message);
      case MessageType.custom:
        return _onReceiveCustomMessage(message);
      default:
        return Left(
          AppException(
            statusCode: 400,
            message: 'Unsupported message type: ${message.type}',
            identifier: 'unsupported_message_type',
          ),
        );
    }
  }

  Future<ResultWithData<Message>> _onReceiveSystemMessage(
      Message message) async {
    final systemMessage = message as SystemMessage;

    return Right(
      systemMessage.copyWith(
        text: systemMessage.text.replaceAll('receiver', 'other side'),
      ),
    );
  }

  Future<ResultWithData<Message>> _onReceiveCustomMessage(
      Message message) async {
    final customMessage = message as CustomMessage;
    if (customMessage.extend.subtype == MessageSubtype.DEVICE_CHANGE) {
      return Right(customMessage);
    } else if (customMessage.extend.subtype == MessageSubtype.VOICE_CALL) {
      return Right(customMessage);
    } else if (customMessage.extend.subtype == MessageSubtype.INVITE_ROOM) {
      return Right(customMessage);
    }

    return Left(
      AppException(
        statusCode: 400,
        message: 'Unsupported message type: ${message.type}',
        identifier: 'unsupported_message_type',
      ),
    );
  }

  Future<ResultWithData<Message>> _decryptTextMessage(Message message) async {
    final textMessage = message as TextMessage;
    final localUserId = _getLocalUserId();

    if (localUserId == null) {
      return _createLocalUserNullError();
    }

    try {
      final text =
          await ref.read(signalMessageProvider.notifier).decryptMessage(
                localUserId,
                conversationId,
                textMessage.text,
                textMessage.extend.isPreKeyMessage,
              );

      return Right(textMessage.copyWith(
        text: text,
      ));
    } catch (e) {
      return Left(
        AppException(
          statusCode: 500,
          message: 'Failed to decrypt text message: $e',
          identifier: 'decrypt_text_message_failed',
        ),
      );
    }
  }

  /// Unified method to process media messages (image, audio, video)
  Future<ResultWithData<Message>> _processMediaMessage<T extends Message>(
    T mediaMessage,
    String logTag,
  ) async {
    // Get local user ID
    final localUserId = _getLocalUserId();
    if (localUserId == null) {
      return _createLocalUserNullError();
    }

    // Get or create file metadata
    final fileMetadata = await getOrCreateFileMetadata(mediaMessage);
    if (fileMetadata == null) {
      return Left(
        AppException(
          statusCode: 400,
          message: 'Failed to get or create file metadata',
          identifier: '${mediaMessage.type.name}_file_metadata_not_found',
        ),
      );
    }

    try {
      // If file is already downloaded
      if (mediaMessage.extend.fileStatus == FileStatus.downloaded) {
        return Right(_createDownloadedMessage(
          mediaMessage,
          fileMetadata,
          FileStatus.downloaded,
        ));
      }

      // File needs processing
      final filePath = fileMetadata.path;
      LogUtils.d('Processing ${mediaMessage.type.name} file: $filePath',
          tag: logTag);

      // Update status to downloading
      await _updateMessageFileStatus(
        mediaMessage,
        fileMetadata.id,
        FileStatus.downloading,
      );

      // After successful processing, return message with downloaded status
      final processedMessage = _createDownloadedMessage(
        mediaMessage,
        fileMetadata,
        FileStatus.downloaded,
      );

      return Right(processedMessage);
    } catch (e) {
      LogUtils.e('Error processing ${mediaMessage.type.name}: $e', tag: logTag);
      
      // Update status to failed
      await _updateMessageFileStatus(
        mediaMessage,
        fileMetadata.id,
        FileStatus.failed,
      );

      return Left(
        AppException(
          statusCode: 500,
          message: 'Failed to process ${mediaMessage.type.name}: $e',
          identifier: '${mediaMessage.type.name}_processing_failed',
        ),
      );
    }
  }

  /// Helper to create a message with updated file status
  T _createDownloadedMessage<T extends Message>(
    T message,
    FileMetadata fileMetadata,
    FileStatus status,
  ) {
    final updatedMetadata = message.extend.copyWith(
      fileId: fileMetadata.id,
      fileStatus: status,
    );

    if (message is AudioMessage) {
      return message.copyWith(
        name: fileMetadata.name,
        size: fileMetadata.size,
        metadata: updatedMetadata.toJson(),
      ) as T;
    } else if (message is ImageMessage) {
      return message.copyWith(
        name: fileMetadata.name,
        size: fileMetadata.size,
        metadata: updatedMetadata.toJson(),
      ) as T;
    } else if (message is VideoMessage) {
      return message.copyWith(
        name: fileMetadata.name,
        size: fileMetadata.size,
        metadata: updatedMetadata.toJson(),
      ) as T;
    } else {
      return message.copyWith(
        metadata: updatedMetadata.toJson(),
      ) as T;
    }
  }

  /// Update message with new file status
  Future<void> _updateMessageFileStatus(
    Message message,
    String fileId,
    FileStatus status,
  ) async {
    final updatedMetadata = message.extend.copyWith(
      fileId: fileId,
      fileStatus: status,
    );

    await updateMessage(
      message.copyWith(
        metadata: updatedMetadata.toJson(),
      ),
    );
  }

  /// Get local user ID with null safety
  String? _getLocalUserId() {
    return ref.read(accountProvider).userInfo?.profile?.id;
  }

  /// Create standard error for null local user
  ResultWithData<Message> _createLocalUserNullError() {
    return const Left(
      AppException(
        statusCode: 400,
        message: 'Local user ID is null',
        identifier: 'local_user_id_null',
      ),
    );
  }

  // 根据消息类型获取文件类型
  FileType _getFileTypeFromMessageType(MessageType type) {
    switch (type) {
      case MessageType.image:
        return FileType.image;
      case MessageType.video:
        return FileType.video;
      case MessageType.audio:
        return FileType.audio;
      default:
        return FileType.file;
    }
  }

  Future<FileMetadata?> getOrCreateFileMetadata(Message message) async {
    final fileId = message.extend.fileId;
    if (fileId == null || fileId.isEmpty) {
      final fileUrl = message.extend.fileUrl;
      final thumbnailUrl = message.extend.thumbnailUrl;
      if (fileUrl == null) {
        return null;
      }
      
      // 获取本地用户ID和发送者ID，用于解密
      final localUserId = _getLocalUserId();
      final senderId = message.author.id;

      // 创建解密回调函数
      FileDecryptCallback? decryptCallback;
      if (localUserId != null) {
        decryptCallback = (List<int> encryptedData) async {
          try {
            final signalMessage = ref.read(signalMessageProvider.notifier);
            final decryptedData = await signalMessage.decryptData(
              localUserId,
              senderId,
              encryptedData,
              message.extend.isPreKeyMessage,
            );
            return decryptedData;
          } catch (e) {
            LogUtils.e('Failed to decrypt file data: $e',
                tag: 'receiveMessage');
            // 解密失败时返回原始数据
            return encryptedData;
          }
        };
      }
      
      final fileMetadata = await getIt<IFileService>().saveFromUrl(
        url: fileUrl,
        thumbnailUrl: thumbnailUrl,
        type: _getFileTypeFromMessageType(message.type),
        conversationId: conversationId,
        decryptCallback: decryptCallback,
      );

      return fileMetadata.fold((left) async {
        LogUtils.e('Failed to save file from url: $left',
            tag: 'receiveMessage');
        final newMetadata = message.extend.copyWith(
          fileStatus: FileStatus.failed,
        );

        await updateMessage(
          message.copyWith(
            metadata: newMetadata.toJson(),
          ),
        );
        return null;
      }, (right) async {
        LogUtils.d('Saved file from url: ${right.toJson().toString()}',
            tag: 'receiveMessage');
        final newMetadata = message.extend.copyWith(
          fileId: right.id,
          fileStatus: FileStatus.downloaded,
        );

        await updateMessage(
          message.copyWith(
            metadata: newMetadata.toJson(),
          ),
        );
        return right;
      });
    }

    final file = await getIt<IFileService>().getFileMetadata(fileId);

    return file.getRight();
  }
}
