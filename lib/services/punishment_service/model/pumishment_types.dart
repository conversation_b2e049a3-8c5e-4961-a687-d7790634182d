import 'package:flutter_audio_room/shared/constants/business_error_codes.dart';

enum PunishmentType {
  ivcPunished(BusinessErrorCodes.instantVoiceCallPunishment),
  icPunished(BusinessErrorCodes.instantChatPunishment),
  audioRoomCreatePunished(BusinessErrorCodes.audioRoomCreatePunishment),
  audioRoomJoinPunished(BusinessErrorCodes.audioRoomJoinPunishment);

  final String code;

  const PunishmentType(this.code);
}

enum RestrictType {
  userBanned(BusinessErrorCodes.userBanned),
  loginPunished(BusinessErrorCodes.userLoginPunished);

  final String code;

  const RestrictType(this.code);
}
