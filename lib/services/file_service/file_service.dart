import 'dart:convert';
import 'dart:io';

import 'package:dio/dio.dart';
import 'package:flutter_audio_room/core/utils/log_utils.dart';
import 'package:flutter_audio_room/services/file_service/file_failure.dart';
import 'package:flutter_audio_room/services/file_service/i_file_service.dart';
import 'package:flutter_audio_room/shared/domain/models/either.dart';
import 'package:flutter_audio_room/shared/domain/types/common_types.dart';
import 'package:path/path.dart' as path;
import 'package:path_provider/path_provider.dart';
import 'package:uuid/uuid.dart';

class FileService implements IFileService {
  static const int _maxCacheSize = 100 * 1024 * 1024; // 100MB
  static const Duration _defaultCacheAge = Duration(days: 7);

  final Map<String, FileMetadata> _fileMetadata = {};
  final Uuid _uuid = const Uuid();
  final Dio _dio;

  late final Directory _baseDir;
  late final Directory _cacheDir;
  late final Directory _thumbnailDir;
  late final Directory _tempDir;

  @override
  Directory get tempDir => _tempDir;

  @override
  Directory get appDir => _baseDir;

  @override
  Directory get cacheDir => _cacheDir;

  @override
  Directory get thumbnailDir => _thumbnailDir;

  FileService({
    Dio? dio,
  }) : _dio = dio ?? Dio();

  @override
  Future<VoidResult> initialize() async {
    try {
      // Get application documents directory
      final appDir = await getApplicationDocumentsDirectory();
      _baseDir = Directory(path.join(appDir.path, 'managed_files'));
      LogUtils.d('baseDir: ${_baseDir.path}', tag: 'FileService');
      _cacheDir = Directory(path.join(appDir.path, 'file_cache'));
      _thumbnailDir = Directory(path.join(appDir.path, 'thumbnails'));
      _tempDir = Directory(path.join(appDir.path, 'temp'));

      // Create directories if they don't exist
      await Future.wait([
        _baseDir.create(recursive: true),
        _cacheDir.create(recursive: true),
        _thumbnailDir.create(recursive: true),
        _tempDir.create(recursive: true),
      ]);

      // Load existing file metadata
      await _loadMetadata();

      return const Right(null);
    } catch (e) {
      return Left(
          InitializationFailure('Failed to initialize file service: $e'));
    }
  }

  Future<void> _loadMetadata() async {
    try {
      final metadataFile = File(path.join(_baseDir.path, 'metadata.json'));
      if (await metadataFile.exists()) {
        final content = await metadataFile.readAsString();
        final jsonData = jsonDecode(content) as Map<String, dynamic>;

        _fileMetadata.clear();
        for (final item in jsonData['files'] as List) {
          var metadata = FileMetadata.fromJson(item as Map<String, dynamic>);
          // Convert relative path to absolute path
          final absolutePath = path.join(_baseDir.path, metadata.path);
          final thumbnailPath =
              path.join(_thumbnailDir.path, metadata.thumbnailPath);
          // Verify file exists
          if (await File(absolutePath).exists()) {
            metadata = metadata.copyWith(path: absolutePath);
          } else {
            LogUtils.w('File not found on disk: $absolutePath',
                tag: 'FileService');
          }
          if (await File(thumbnailPath).exists()) {
            metadata = metadata.copyWith(thumbnailPath: thumbnailPath);
          } else {
            LogUtils.w('Thumbnail not found on disk: $thumbnailPath',
                tag: 'FileService');
          }

          _fileMetadata[metadata.id] = metadata;
        }
      }
    } catch (e) {
      LogUtils.e('Failed to load metadata: $e', tag: 'FileService');
    }
  }

  Future<void> _saveMetadata() async {
    try {
      final metadataFile = File(path.join(_baseDir.path, 'metadata.json'));
      final jsonData = {
        'files': _fileMetadata.values.map((m) {
          // Convert absolute path to relative path before saving
          final relativePath = path.relative(m.path, from: _baseDir.path);
          var thumbnailPath = m.thumbnailPath;
          if (thumbnailPath != null) {
            thumbnailPath =
                path.relative(thumbnailPath, from: _thumbnailDir.path);
          }
          return m
              .copyWith(path: relativePath, thumbnailPath: thumbnailPath)
              .toJson();
        }).toList(),
      };
      await metadataFile.writeAsString(jsonEncode(jsonData));
    } catch (e) {
      LogUtils.e('Failed to save metadata: $e', tag: 'FileService');
    }
  }

  // Get relative path from app directory
  @override
  String getRelativePath(String filePath) {
    return path.relative(filePath, from: _baseDir.path);
  }

  // Get full path from relative path
  @override
  String getFullPath(String relativePath) {
    return path.join(_baseDir.path, relativePath);
  }

  @override
  Future<ResultWithData<FileMetadata>> saveFile({
    required File file,
    required FileType type,
    String? thumbnailPath,
    String? customName,
    String? conversationId,
  }) async {
    try {
      final String fileId = _uuid.v4();
      final String fileName = customName ?? path.basename(file.path);
      final String targetPath = path.join(_baseDir.path, type.name, fileName);

      // Create type directory if it doesn't exist
      await Directory(path.dirname(targetPath)).create(recursive: true);

      // Copy file to target location
      final targetFile = await file.copy(targetPath);

      final metadata = FileMetadata(
        id: fileId,
        name: fileName,
        path: targetFile.path,
        thumbnailPath: thumbnailPath,
        type: type,
        size: await targetFile.length(),
        createdAt: DateTime.now(),
        conversationId: conversationId,
      );

      _fileMetadata[fileId] = metadata;
      await _saveMetadata();

      return Right(metadata);
    } catch (e) {
      return Left(SaveFailure('Failed to save file: $e'));
    }
  }

  @override
  Future<ResultWithData<(File, FileMetadata)>> getFile(String fileId) async {
    try {
      final metadata = _fileMetadata[fileId];
      if (metadata == null) {
        return Left(NotFoundFailure('File not found'));
      }

      final file = File(metadata.path);
      if (!await file.exists()) {
        return Left(NotFoundFailure('File not found on disk'));
      }

      return Right((file, metadata));
    } catch (e) {
      return Left(RetrievalFailure('Failed to get file: $e'));
    }
  }

  @override
  Future<VoidResult> deleteFile(String fileId) async {
    try {
      final metadata = _fileMetadata[fileId];
      if (metadata == null) {
        return Left(NotFoundFailure('File not found'));
      }

      final file = File(metadata.path);
      if (await file.exists()) {
        await file.delete();
      }

      if (metadata.thumbnailPath != null) {
        final thumbnailFile = File(metadata.thumbnailPath!);
        if (await thumbnailFile.exists()) {
          await thumbnailFile.delete();
        }
      }

      _fileMetadata.remove(fileId);
      await _saveMetadata();

      return const Right(null);
    } catch (e) {
      return Left(DeletionFailure('Failed to delete file: $e'));
    }
  }

  @override
  Future<ResultWithData<FileMetadata>> getFileMetadata(String fileId) async {
    final metadata = _fileMetadata[fileId];
    if (metadata == null) {
      return Left(NotFoundFailure('File metadata not found'));
    }
    return Right(metadata);
  }

  @override
  Future<ResultWithData<List<FileMetadata>>> listFiles({FileType? type}) async {
    try {
      if (type != null) {
        return Right(_fileMetadata.values
            .where((metadata) => metadata.type == type)
            .toList());
      }
      return Right(_fileMetadata.values.toList());
    } catch (e) {
      return Left(RetrievalFailure('Failed to list files: $e'));
    }
  }

  @override
  Future<ResultWithData<StorageStats>> getStorageStats() async {
    try {
      final Map<FileType, int> sizeByType = {};
      int totalSize = 0;

      for (final metadata in _fileMetadata.values) {
        final size = await File(metadata.path).length();
        sizeByType[metadata.type] = (sizeByType[metadata.type] ?? 0) + size;
        totalSize += size;
      }

      final stats = StorageStats(
        totalSize: totalSize,
        availableSize: await _getAvailableSpace(),
        sizeByType: sizeByType,
        fileCount: _fileMetadata.length,
      );

      return Right(stats);
    } catch (e) {
      return Left(RetrievalFailure('Failed to get storage stats: $e'));
    }
  }

  Future<int> _getAvailableSpace() async {
    try {
      final ProcessResult result =
          await Process.run('df', ['-k', _baseDir.path]);
      final lines = result.stdout.toString().split('\n');
      if (lines.length >= 2) {
        final values = lines[1].split(RegExp(r'\s+'));
        return int.parse(values[3]) * 1024; // Convert from KB to bytes
      }
      return 0;
    } catch (e) {
      LogUtils.e('Failed to get available space: $e', tag: 'FileService');
      return 0;
    }
  }

  @override
  Future<VoidResult> clearCache({
    Duration? olderThan,
    int? keepMaxSize,
    List<FileType>? fileTypes,
  }) async {
    try {
      final now = DateTime.now();
      final maxAge = olderThan ?? _defaultCacheAge;
      final maxSize = keepMaxSize ?? _maxCacheSize;

      final filesToDelete = _fileMetadata.values.where((metadata) {
        if (fileTypes != null && !fileTypes.contains(metadata.type)) {
          return false;
        }
        return now.difference(metadata.createdAt) > maxAge;
      }).toList();

      // Sort by last accessed time
      filesToDelete.sort((a, b) {
        final aTime = a.lastAccessed ?? a.createdAt;
        final bTime = b.lastAccessed ?? b.createdAt;
        return aTime.compareTo(bTime);
      });

      int totalFreed = 0;
      for (final metadata in filesToDelete) {
        if (totalFreed >= maxSize) break;
        final result = await deleteFile(metadata.id);
        if (result.isRight()) {
          totalFreed += metadata.size;
        }
      }

      return const Right(null);
    } catch (e) {
      return Left(CacheFailure('Failed to clear cache: $e'));
    }
  }

  @override
  Future<ResultWithData<bool>> exists(String fileId) async {
    try {
      final metadata = _fileMetadata[fileId];
      if (metadata == null) return const Right(false);

      final file = File(metadata.path);
      return Right(await file.exists());
    } catch (e) {
      return Left(ValidationFailure('Failed to check file existence: $e'));
    }
  }

  @override
  Future<VoidResult> moveFile({
    required String fileId,
    required String newPath,
  }) async {
    try {
      final metadata = _fileMetadata[fileId];
      if (metadata == null) {
        return Left(NotFoundFailure('File not found'));
      }

      final file = File(metadata.path);
      if (!await file.exists()) {
        return Left(NotFoundFailure('File not found on disk'));
      }

      final newFile = await file.rename(newPath);
      _fileMetadata[fileId] = metadata.copyWith(path: newFile.path);
      await _saveMetadata();

      return const Right(null);
    } catch (e) {
      return Left(MoveFailure('Failed to move file: $e'));
    }
  }

  @override
  Future<ResultWithData<FileMetadata>> copyFile({
    required String fileId,
    required String newPath,
  }) async {
    try {
      final metadata = _fileMetadata[fileId];
      if (metadata == null) {
        return Left(NotFoundFailure('File not found'));
      }

      final file = File(metadata.path);
      if (!await file.exists()) {
        return Left(NotFoundFailure('File not found on disk'));
      }

      final newFile = await file.copy(newPath);
      final newId = _uuid.v4();

      final newMetadata = metadata.copyWith(
        id: newId,
        path: newFile.path,
        createdAt: DateTime.now(),
      );

      _fileMetadata[newId] = newMetadata;
      await _saveMetadata();

      return Right(newMetadata);
    } catch (e) {
      return Left(CopyFailure('Failed to copy file: $e'));
    }
  }

  @override
  Future<ResultWithData<int>> getTotalSize({List<FileType>? types}) async {
    try {
      int totalSize = 0;
      for (final metadata in _fileMetadata.values) {
        if (types == null || types.contains(metadata.type)) {
          final file = File(metadata.path);
          if (await file.exists()) {
            totalSize += await file.length();
          }
        }
      }
      return Right(totalSize);
    } catch (e) {
      return Left(RetrievalFailure('Failed to get total size: $e'));
    }
  }

  @override
  Future<VoidResult> cleanup({
    required Map<FileType, Duration> retentionPeriods,
    Map<FileType, int>? maxSizePerType,
  }) async {
    try {
      final now = DateTime.now();
      final filesToDelete = <String>[];

      // Check retention periods
      for (final entry in retentionPeriods.entries) {
        final type = entry.key;
        final period = entry.value;

        final typeFiles = _fileMetadata.values
            .where((m) => m.type == type)
            .where((m) => now.difference(m.createdAt) > period)
            .map((m) => m.id)
            .toList();

        filesToDelete.addAll(typeFiles);
      }

      // Check size limits
      if (maxSizePerType != null) {
        for (final entry in maxSizePerType.entries) {
          final type = entry.key;
          final maxSize = entry.value;

          final typeFiles =
              _fileMetadata.values.where((m) => m.type == type).toList();

          int totalSize = 0;
          for (final metadata in typeFiles) {
            totalSize += metadata.size;
          }

          if (totalSize > maxSize) {
            // Sort by last accessed, oldest first
            typeFiles.sort((a, b) {
              final aTime = a.lastAccessed ?? a.createdAt;
              final bTime = b.lastAccessed ?? b.createdAt;
              return aTime.compareTo(bTime);
            });

            // Delete oldest files until under limit
            for (final metadata in typeFiles) {
              if (totalSize <= maxSize) break;
              filesToDelete.add(metadata.id);
              totalSize -= metadata.size;
            }
          }
        }
      }

      // Delete files
      for (final fileId in filesToDelete) {
        final result = await deleteFile(fileId);
        if (result.isLeft()) {
          LogUtils.w('Failed to delete file $fileId during cleanup',
              tag: 'FileService');
        }
      }

      return const Right(null);
    } catch (e) {
      return Left(CleanupFailure('Failed to cleanup files: $e'));
    }
  }

  @override
  Future<ResultWithData<String>> exportFile({
    required String fileId,
    required String destinationPath,
  }) async {
    try {
      final metadata = _fileMetadata[fileId];
      if (metadata == null) {
        return Left(NotFoundFailure('File not found'));
      }

      final file = File(metadata.path);
      if (!await file.exists()) {
        return Left(NotFoundFailure('File not found on disk'));
      }

      final exportedFile = await file.copy(destinationPath);
      return Right(exportedFile.path);
    } catch (e) {
      return Left(ExportFailure('Failed to export file: $e'));
    }
  }

  @override
  Future<ResultWithData<FileMetadata>> importFile({
    required String sourcePath,
    String? thumbnailPath,
    required FileType type,
    String? conversationId,
  }) async {
    try {
      final file = File(sourcePath);
      if (!await file.exists()) {
        return Left(NotFoundFailure('Source file not found'));
      }

      return saveFile(
        file: file,
        type: type,
        conversationId: conversationId,
        thumbnailPath: thumbnailPath,
      );
    } catch (e) {
      return Left(ImportFailure('Failed to import file: $e'));
    }
  }

  @override
  Future<VoidResult> dispose() async {
    try {
      await _saveMetadata();
      return const Right(null);
    } catch (e) {
      return Left(DisposalFailure('Failed to dispose file service: $e'));
    }
  }

  @override
  Future<ResultWithData<FileMetadata>> saveBytes({
    required List<int> bytes,
    required String fileName,
    required FileType type,
    String? conversationId,
  }) async {
    try {
      final String fileId = _uuid.v4();
      final String targetPath = path.join(_baseDir.path, type.name, fileId);

      // Create type directory if it doesn't exist
      await Directory(path.dirname(targetPath)).create(recursive: true);

      // Write bytes to file
      final targetFile = File(targetPath);
      await targetFile.writeAsBytes(bytes);

      final metadata = FileMetadata(
        id: fileId,
        name: fileName,
        path: targetFile.path,
        type: type,
        size: bytes.length,
        createdAt: DateTime.now(),
        conversationId: conversationId,
      );

      _fileMetadata[fileId] = metadata;
      await _saveMetadata();

      return Right(metadata);
    } catch (e) {
      return Left(SaveFailure('Failed to save bytes: $e'));
    }
  }

  @override
  Future<ResultWithData<T>> withTempFile<T>({
    required List<int> bytes,
    required String fileName,
    required Future<ResultWithData<T>> Function(File tempFile) callback,
  }) async {
    final String tempPath = path.join(_tempDir.path, '${_uuid.v4()}_$fileName');
    final tempFile = File(tempPath);

    try {
      // Write bytes to temp file
      await tempFile.writeAsBytes(bytes);

      // Execute callback
      final result = await callback(tempFile);

      // Delete temp file
      if (await tempFile.exists()) {
        await tempFile.delete();
      }

      return result;
    } catch (e) {
      // Clean up temp file in case of error
      if (await tempFile.exists()) {
        try {
          await tempFile.delete();
        } catch (deleteError) {
          LogUtils.e('Failed to delete temp file: $deleteError',
              tag: 'FileService');
        }
      }
      return Left(TempFileFailure('Failed to process temp file: $e'));
    }
  }

  @override
  Future<ResultWithData<FileMetadata>> saveFromUrl({
    required String url,
    required FileType type,
    String? customName,
    Map<String, String>? headers,
    DownloadProgressCallback? onProgress,
    String? thumbnailUrl,
    FileDecryptCallback? decryptCallback,
    String? conversationId,
  }) async {
    try {
      final String fileId = _uuid.v4();
      final nameWithoutQuery = url.split('?')[0];
      final String fileName = customName ?? path.basename(nameWithoutQuery);
      final String tempPath =
          path.join(_tempDir.path, '${_uuid.v4()}_$fileName');

      // 对于音频文件，强制使用.aac扩展名（因为我们现在录音使用AAC格式）
      String fileExtension = path.extension(fileName);
      if (type == FileType.audio && fileExtension == '.mp3') {
        fileExtension = '.aac';
      }

      final String targetPath = path.join(
        _baseDir.path,
        type.name,
        fileId + fileExtension,
      );

      // Create directories if they don't exist
      await Directory(path.dirname(targetPath)).create(recursive: true);

      // Download file to temp location
      final response = await _dio.download(
        url,
        tempPath,
        options: Options(headers: headers),
        onReceiveProgress: onProgress,
      );

      if (response.statusCode != 200) {
        return Left(
            SaveFailure('Failed to download file: ${response.statusCode}'));
      }

      final tempFile = File(tempPath);
      if (!await tempFile.exists()) {
        return Left(SaveFailure('Downloaded file not found'));
      }

      try {
        // 如果提供了解密回调，执行解密操作
        if (decryptCallback != null) {
          // 读取加密文件内容
          final encryptedBytes = await tempFile.readAsBytes();
          // 调用解密回调
          final decryptedBytes = await decryptCallback(encryptedBytes);

          // 将解密后的内容写回临时文件
          await tempFile.writeAsBytes(decryptedBytes);
        }

        // Move downloaded file to target location
        final targetFile = await tempFile.copy(targetPath);

        // Create initial metadata
        var metadata = FileMetadata(
          id: fileId,
          name: fileName,
          path: targetFile.path,
          type: type,
          size: await targetFile.length(),
          createdAt: DateTime.now(),
          conversationId: conversationId,
        );

        // Download thumbnail from URL if provided
        if (thumbnailUrl != null) {
          final thumbnailFileName =
              '${fileId}_thumb${path.extension(thumbnailUrl)}';
          final thumbnailTempPath =
              path.join(_tempDir.path, '${_uuid.v4()}_$thumbnailFileName');
          final thumbnailTargetPath =
              path.join(_thumbnailDir.path, thumbnailFileName);

          try {
            // Download thumbnail
            final thumbnailResponse = await _dio.download(
              thumbnailUrl,
              thumbnailTempPath,
              options: Options(headers: headers),
            );

            if (thumbnailResponse.statusCode == 200) {
              final thumbnailTempFile = File(thumbnailTempPath);
              if (await thumbnailTempFile.exists()) {
                // 如果提供了解密回调，也对缩略图执行解密操作
                if (decryptCallback != null) {
                  // 读取加密缩略图内容
                  final encryptedThumbnailBytes =
                      await thumbnailTempFile.readAsBytes();
                  // 调用解密回调
                  final decryptedThumbnailBytes =
                      await decryptCallback(encryptedThumbnailBytes);
                  // 将解密后的内容写回临时缩略图文件
                  await thumbnailTempFile.writeAsBytes(decryptedThumbnailBytes);
                  LogUtils.d('Thumbnail decrypted successfully',
                      tag: 'FileService');
                }

                // Create thumbnails directory if it doesn't exist
                await Directory(path.dirname(thumbnailTargetPath))
                    .create(recursive: true);

                // Move thumbnail to target location
                final thumbnailFile =
                    await thumbnailTempFile.copy(thumbnailTargetPath);

                // Update metadata with thumbnail path
                metadata = metadata.copyWith(thumbnailPath: thumbnailFile.path);

                // Clean up temp thumbnail file
                if (await thumbnailTempFile.exists()) {
                  await thumbnailTempFile.delete();
                }
              }
            } else {
              LogUtils.w(
                  'Failed to download thumbnail: ${thumbnailResponse.statusCode}',
                  tag: 'FileService');
            }
          } catch (e) {
            LogUtils.w('Error downloading thumbnail: $e', tag: 'FileService');
            // Continue without failing the main file download
          }
        }

        _fileMetadata[fileId] = metadata;
        await _saveMetadata();

        return Right(metadata);
      } finally {
        // Clean up temp file
        if (await tempFile.exists()) {
          await tempFile.delete();
        }
      }
    } catch (e) {
      return Left(SaveFailure('Failed to save file from URL: $e'));
    }
  }
}
