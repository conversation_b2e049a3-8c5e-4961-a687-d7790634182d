import 'dart:async';

import 'package:dio/dio.dart';
import 'package:flutter_audio_room/core/di/app_module.dart';
import 'package:flutter_audio_room/core/utils/log_utils.dart';
import 'package:flutter_audio_room/services/punishment_service/punishment_service.dart';
import 'package:flutter_audio_room/services/token_refresh/token_refresh_service.dart';
import 'package:flutter_audio_room/shared/constants/business_error_codes.dart';
import 'package:flutter_audio_room/shared/data/local/storage_service.dart';
import 'package:flutter_audio_room/shared/exceptions/app_exception.dart';

class RequestInterceptor extends QueuedInterceptor {
  final TokenRefreshService _tokenRefreshService;
  final Dio _dio;

  // 使用单一的刷新状态管理，避免与 TokenRefreshService 冲突
  static bool _isRefreshing = false;
  static final List<_RetryRequest> _pendingRequests = [];

  // 需要退出登录的错误码
  final _shouldLogoutIdentifiers = BusinessErrorCodes.logoutCodes.toList();

  // 需要刷新 token 的错误码
  final _shouldRefreshTokenIdentifiers =
      BusinessErrorCodes.tokenRefreshCodes.toList();

  RequestInterceptor(this._tokenRefreshService, this._dio);

  @override
  void onRequest(RequestOptions options, RequestInterceptorHandler handler) {
    handler.next(options);
  }

  @override
  void onResponse(Response response, ResponseInterceptorHandler handler) async {
    final success = response.data['success'];

    if (success != true) {
      final code = response.data['code'];
      final message = response.data['msg'];
      LogUtils.e(
        'code: $code, msg: $message',
        tag: 'RequestInterceptor',
      );
    }

    try {
      if (response.data is Map<String, dynamic>) {
        final code = response.data['code'] ?? '';

        // 处理惩罚相关错误码
        if (getIt<PunishmentService>().isRestrictCode(code)) {
          _tokenRefreshService.toLogin();
          return handler.reject(
            DioException(
              requestOptions: response.requestOptions,
              error: const AppException(
                message: 'Punishment',
                statusCode: 401,
                identifier: 'RequestInterceptor',
              ),
            ),
          );
        }
        // 处理需要刷新 token 的错误码
        else if (_shouldRefreshTokenIdentifiers.contains(code)) {
          final retryResponse =
              await _handleTokenRefreshAndRetry(response.requestOptions);
          return handler.resolve(retryResponse);
        }
        // 处理 access.token.replaced 错误 - 在刷新期间可以重试
        else if (_shouldLogoutIdentifiers.contains(code)) {
          // 如果当前正在刷新 token，将请求加入队列等待重试
          if (_isRefreshing) {
            LogUtils.d(
              'Received access.token.replaced during token refresh, queuing for retry',
              tag: 'RequestInterceptor',
            );
            final retryResponse =
                await _handleTokenRefreshAndRetry(response.requestOptions);
            return handler.resolve(retryResponse);
          } else {
            // 否则直接退出登录
            final message = response.data['msg'];
            _tokenRefreshService.toLogin(message: message);
            return handler.reject(
              DioException(
                requestOptions: response.requestOptions,
                error: AppException(
                  message: message,
                  statusCode: 401,
                  identifier: 'RequestInterceptor',
                ),
              ),
            );
          }
        }
      }
    } catch (e) {
      return handler.reject(
        DioException(
          requestOptions: response.requestOptions,
          error: e,
        ),
      );
    }

    return handler.next(response);
  }

  Future<Response> _handleTokenRefreshAndRetry(
      RequestOptions requestOptions) async {
    final requestCompleter = Completer<Response>();

    // 将请求加入待处理队列
    _pendingRequests.add(_RetryRequest(
      requestOptions: requestOptions,
      completer: requestCompleter,
    ));

    // 如果没有正在刷新，启动刷新流程
    if (!_isRefreshing) {
      _startTokenRefresh();
    }

    // 等待请求完成（无论是否正在刷新，都会在 _processPendingRequests 中处理）
    return await requestCompleter.future;
  }

  /// 启动 token 刷新流程并处理所有待处理的请求
  void _startTokenRefresh() async {
    _isRefreshing = true;

    LogUtils.d(
      'Starting token refresh process',
      tag: 'RequestInterceptor',
    );

    try {
      final result = await _tokenRefreshService.refreshToken();

      final refreshSuccess = result.fold(
        (exception) {
          LogUtils.e(
            'Token refresh failed: ${exception.message}',
            tag: 'RequestInterceptor',
          );
          return false;
        },
        (newSession) {
          LogUtils.d(
            'Token refresh successful',
            tag: 'RequestInterceptor',
          );
          return true;
        },
      );

      // 处理所有待处理的请求
      await _processPendingRequests(refreshSuccess);
    } catch (e) {
      LogUtils.e(
        'Unexpected error during token refresh: $e',
        tag: 'RequestInterceptor',
      );
      await _processPendingRequests(false);
    } finally {
      _isRefreshing = false;
    }
  }

  /// 处理所有待处理的请求
  Future<void> _processPendingRequests(bool refreshSuccess) async {
    LogUtils.d(
      'Processing ${_pendingRequests.length} pending requests (refresh success: $refreshSuccess)',
      tag: 'RequestInterceptor',
    );

    final requests = List<_RetryRequest>.from(_pendingRequests);
    _pendingRequests.clear();

    for (final request in requests) {
      try {
        if (refreshSuccess) {
          // token 刷新成功，重试请求
          final response = await _retryRequest(request.requestOptions);
          request.completer.complete(response);
        } else {
          // token 刷新失败，返回错误
          request.completer.completeError(
            DioException(
              requestOptions: request.requestOptions,
              error: const AppException(
                message: 'Token refresh failed',
                statusCode: 401,
                identifier: 'RequestInterceptor',
              ),
            ),
          );
        }
      } catch (e) {
        request.completer.completeError(e);
      }
    }
  }

  /// 使用新的 access token 重试请求
  Future<Response> _retryRequest(RequestOptions requestOptions) async {
    final accessToken = getIt<StorageService>().accessToken;

    LogUtils.d(
      'Retrying request: ${requestOptions.method} ${requestOptions.path}',
      tag: 'RequestInterceptor',
    );

    final newOptions = requestOptions.copyWith(
      headers: {
        ...requestOptions.headers,
        'Authorization': 'Bearer $accessToken',
      },
    );

    try {
      final response = await _dio.fetch(newOptions);
      LogUtils.d(
        'Request retry successful: ${requestOptions.method} ${requestOptions.path}',
        tag: 'RequestInterceptor',
      );
      return response;
    } catch (e) {
      LogUtils.e(
        'Request retry failed: ${requestOptions.method} ${requestOptions.path}, error: $e',
        tag: 'RequestInterceptor',
      );
      rethrow;
    }
  }
}

class _RetryRequest {
  final RequestOptions requestOptions;
  final Completer<Response> completer;

  _RetryRequest({
    required this.requestOptions,
    required this.completer,
  });
}
